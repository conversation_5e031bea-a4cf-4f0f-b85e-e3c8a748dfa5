# mypy: ignore-errors
import asyncio
import statemachine
from typing import List, Awaitable

from heracles.core.utils import add_asyncio_handler, cancel_asyncio_handler
from heracles.core.schema import ErrorHandlerMessageType
from heracles.core.exceptions import AgentRunException, UserTaskStatusException, UserTaskAppendLimitException


class RoleUserEventsMixin:  # type: ignore

    async def on_u_make_spec(self, goal: str, goal_detail: str = ''):
        """ 事件处理: 开始需求分析, 准备计划前 proposed_list
        """
        self._add_asyncio_task(self.make_spec(goal, goal_detail=goal_detail), ErrorHandlerMessageType.SPEC)

    async def on_u_make_plan(self, goal: str, goal_detail: str = '', proposed_list: List[str] = None):
        """ 事件处理: 制定计划, 成功时生成 Task
        """
        if not self.task_state.planning.is_active:
            self.task_state.plan_task()

        if proposed_list is None:
            proposed_list = []
        self._add_asyncio_task(self.make_plan(goal, goal_detail=goal_detail, proposed_list=proposed_list), ErrorHandlerMessageType.PLAN)

    async def on_u_init_empty_task(self, title: str, description=''):
        if not self.task_state.planning.is_active:
            self.task_state.plan_task()
        await self.init_empty_task(title=title, description=description)

    async def on_u_start_task(self, delay_time=3):
        """ event: 开始任务
            支持：task 万一出现异常，可继续调用这个接口重起
        """
        if not self.task:
            raise UserTaskStatusException('No tasks to execute')
        if self._has_another_task_running():
            raise AgentRunException('Another task is running, please wait and try again later.')
        if not self.task_state.working.is_active:
            self.task_state.start_task()
        self._add_asyncio_task(self.run_task(delay_time=delay_time), ErrorHandlerMessageType.TASK)

    async def on_u_pause_task(self):
        """ event: 暂停任务
        """
        try:
            self.task_state.set_state(self.task_state.pausing)
        except statemachine.exceptions.TransitionNotAllowed as e:
            raise UserTaskStatusException(str(e)) from None
        # 标记暂停后, 等待 200ms, "优雅" 关闭任务
        await asyncio.sleep(0.2)
        if self._asyncio_task:
            self.logger.info("on_u_pause_task: cancel the task directly")
            self._cancel_asyncio_task()
        if self._asyncio_auto_fix_task:
            self._cancel_asyncio_auto_fix_task()

    async def on_u_resume_task(self, delay_time=3):
        """ event: 恢复暂停中的任务
        """
        try:
            self.task_state.resume_task()
        except statemachine.exceptions.TransitionNotAllowed as e:
            raise UserTaskStatusException(str(e)) from None
        self._add_asyncio_task(self.run_task(delay_time=delay_time), ErrorHandlerMessageType.TASK)

    async def on_u_cancel_task(self):
        """ event: 取消当前任务, 并将剩余 TaskAction 标为 CANCELED_STATUS 允许后续重新执行
        """
        try:
            self.task_state.set_state(self.task_state.canceled)
        except statemachine.exceptions.TransitionNotAllowed as e:
            raise UserTaskStatusException(str(e)) from None
        # 标记取消后, 等待 200ms, "优雅" 关闭任务
        await asyncio.sleep(0.2)
        if self._asyncio_task:
            self.logger.info("on_u_cancel_task: cancel the task directly")
            self._cancel_asyncio_task()
        if self._asyncio_auto_fix_task:
            self._cancel_asyncio_auto_fix_task()
        if self.task:
            self.task.cancel_all_actions()

    async def on_u_reset_task(self):
        """ event: 重置并清理所有任务
        """
        self.reset_task()

    async def on_u_add_step(self, step_info):
        """ event: 追加步骤
        """
        if not self.task_state.can_add_step():
            raise UserTaskStatusException('Failed to append step, task need to be in Done/Planning/Canceled/Pausing/Appended state')

        # 检查未开始状态的 task steps 数量
        init_steps_count = len([step for step in self.task.task_steps if step.get_next_runnable_action() is not None])
        # 在planning状态时，不限制增加/追加step和action的数量；否则最大为 3
        if init_steps_count >= 3 and not self.task_state.planning.is_active:
            raise UserTaskStatusException('Failed to append more steps, reached maximum limit of 3 steps to run')

        if self.task_state.appended_count >= self.task_state.appended_limit:
            raise UserTaskAppendLimitException(f"Failed to append more task steps, reached maximum limit of {self.task_state.appended_limit} times") # noqa: E501

        # 除非在planning状态，否则会变为 appended 状态
        if not self.task_state.appended.is_active and not self.task_state.planning.is_active:
            self.task_state.append_task()
        task_step_id = self.task.add_task_step_from_dict(step_info)
        return task_step_id

    async def on_u_add_action(self, task_step_id, action_info):
        """ 事件处理: 追加动作
        """
        if not self.task_state.can_add_action():
            raise UserTaskStatusException('Failed to add action, task need to be in Done/Planning/Canceled state')

        # 除非在planning状态，否则会变为 appended 状态
        if not self.task_state.appended.is_active and not self.task_state.planning.is_active:
            self.task_state.append_task()
        task_action_id = self.task.add_task_action_from_dict(task_step_id, action_info)
        return task_action_id

    async def on_u_modify_step(self, task_step_id, new_title):
        """ 事件处理: 更新step
        """
        if not self.task_state.can_modify_step():
            raise UserTaskStatusException('Failed to modify step, task need to be in Planning/Appended state')
        self.task.modify_task_step(task_step_id, new_title)
        return

    async def on_u_modify_action(self, task_action_id, action_info):
        """ 事件处理: 更新action
        """
        if not self.task_state.can_modify_action():
            raise UserTaskStatusException('Failed to modify action, task need to be in Planning/Appended state')
        self.task.modify_task_action(task_action_id, action_info)
        return

    async def on_u_toggle_revert_action(self, task_action_id):
        """ 事件处理: 标记回滚/恢复action(仅变更状态)
        """
        if not self.task:
            raise UserTaskStatusException('Failed to toggle revert action, task not found')
        task_action = self.task.find_action_by_id(task_action_id)
        if task_action.can_revert():
            task_action.set_status_canceled()
        elif task_action.can_restore():
            task_action.set_status_completed()
        else:
            raise UserTaskStatusException('Failed to toggle revert action, current task action status is not allowed')
        return

    async def on_u_delete_step(self, task_step_id):
        """ 事件处理: 删除step
        """
        if not self.task_state.can_modify_step():
            raise UserTaskStatusException('Failed to delete step, task need to be in Planning/Appended state')
        self.task.delete_task_step(task_step_id)

        # 如果没有可以运行的 action，自动回退到 DONE 状态
        if self.task_state.appended.is_active:
            if not self.task.get_next_runnable_action():
                self.task_state.append_back_task()
        return

    async def on_u_delete_action(self, task_action_id):
        """ 事件处理: 删除action
        """
        if not self.task_state.can_modify_action():
            raise UserTaskStatusException('Failed to delete action, task need to be in Planning/Appended state')
        self.task.delete_task_action(task_action_id)

        # 如果没有可以运行的 action，自动回退到 DONE 状态
        if self.task_state.appended.is_active:
            if not self.task.get_next_runnable_action():
                self.task_state.append_back_task()
        return

    async def on_u_rerun_task_action(self, task_action_id, delay_time=3):
        """ 事件处理: 执行单个动作。仅适用于任务已经完成，且某些动作失败的情况
        """
        if not self.task_state.can_rerun_action():
            raise UserTaskStatusException('Failed to rerun action, task need to be in Done state')
        task_action = self.task.find_action_by_id(task_action_id)
        self._add_asyncio_task(self.rerun_task_action(task_action, delay_time=delay_time), ErrorHandlerMessageType.RERUN)
        return

    async def on_u_cmd_k(self, file_path: str, start_line: int, end_line: int, user_prompt: str, trigger_callback) -> Awaitable[None]:
        """ 事件处理: cmd_k, 注意, 这个函数返回一个协程对象( Awaitable )

        :param file_path: 文件路径
        :param start_line: 开始行
        :param end_line: 结束行
        :param user_prompt: 用户提示词

        :return 注意, 这个函数检查完参数合法后, 返回一个 Awaitable, 调用方应自行启动一个task来调用
        """
        self.cmd_k_role.check_cmd_k_params(file_path, start_line, end_line)
        return self.cmd_k_role.run(file_path, start_line, end_line, user_prompt, trigger_callback)

    async def on_u_code_completion(self, file_path, code_snippet):
        """ 事件处理: 代码补全, TODO
        """
        return await self.cmd_k_role.code_completion_action(file_path, code_snippet)

    async def on_u_build_workspace(self, analyze_item_name='full'):
        """ 事件处理: 构建完整 workspace
        """
        return self._add_asyncio_analyze_role_task(self.build_workspace(analyze_item_name))

    async def on_u_query_workspace_analyze_items(self):
        """ 事件处理: 查询 workspace statusItems
        """
        return self.workspace.detail_status

    async def on_u_reset_message_session(self):
        """ 事件处理: 重置聊天会话, 同时关掉 task 任务
        """
        self.chat_role.reset_session()
        self._cancel_asyncio_task()
        self._cancel_asyncio_auto_fix_task()

    async def on_u_enable_smart_detect(self):
        """ 事件处理: 检查任务
        """
        await self.workspace.smart_detect.set_status('monitoring_errors')
        await self.check_role.run_and_check_project()
        return self.workspace.smart_detect.status

    async def on_u_disable_smart_detect(self):
        """ 事件处理: 停止检查任务
        """
        await self.workspace.smart_detect.set_status('stopped')
        await self.workspace.trigger('message', (
            "Smart detect has been turned off. I will not monitor errors from terminal anymore."
        ))
        return self.workspace.smart_detect.status

    async def on_u_analyze_project_environment(self):
        """ 事件处理: 分析项目环境
        """
        # 如果 basic_info 没有完成，则先完成 basic_info
        if not self.workspace.ensure_basic_info_done():
            # 异常情况下可以重新发起分析
            if not self._asyncio_analyze_role_task:
                self._add_asyncio_analyze_role_task(self.analyze_role.run('project_basic_info'))
        return await self.analyze_role.analyze_project_environment()

    async def run_message_with_wrapper(self, message, *, memory_regenerate, need_consider_task):
        event_emitter = self.workspace.observations.event_emitter
        try:
            if event_emitter.has_listener('message'):
                await event_emitter.emit('message', message)
            else:
                await self.run_message(message, memory_regenerate=memory_regenerate, need_consider_task= need_consider_task)
        finally:
            self.waiting_user_input = True

    async def on_message(self,
            message,
            memory_regenerate=False,
            need_consider_task=False
        ):
        """聊天消息处理"""

        if not self.waiting_user_input:
            self.logger.warning('The current status does not support receiving messages')
            return False
        self.waiting_user_input = False
        add_asyncio_handler(
            self,
            '_asyncio_message',
            self.run_message_with_wrapper(
                message,
                memory_regenerate=memory_regenerate,
                need_consider_task=need_consider_task
            ),
            self.trigger_error_handler,
            ErrorHandlerMessageType.MESSAGE,
            self.logger,
        )
        return True

    async def on_u_stop_message(self):
        """ 事件处理: 停止消息通道任务
        """
        cancel_asyncio_handler(self, '_asyncio_message')
        return True

    async def on_u_run_cmd(self, cmd: str):
        """ 事件处理: 在终端执行命令
        """
        return await self.workspace.tools.run_cmd(cmd)

    async def on_u_expand_task_turn(self, sign_data=None):
        """ 事件处理: 任务步骤扩展, 当调用此接口时, Agent会记录并增加一个任务步骤轮次

        """
        return await self.expand_task_turn(sign_data=sign_data)

    async def on_u_refine_prompt(self, prompt: str, scene: str):
        """事件处理: 润色提示词"""
        return await self.spec_role.refine_prompt(prompt, scene)

    async def on_u_auto_fix_enable(self, enable=True):
        """ 事件处理: 自动修复开关
        """
        await self.workspace.auto_fix.set_auto_fix_enable(enable)

    async def on_u_start_auto_fix(self):
        """ 事件处理: 启动自动修复
        """
        self._add_asyncio_auto_fix_task(self.start_auto_fix_task())
        return True

    async def on_u_stop_auto_fix(self):
        """ 事件处理: 停止自动修复
        """
        await self.workspace.auto_fix.update_status_and_event('fix_stopped')
        self._cancel_asyncio_auto_fix_task()
        return True
