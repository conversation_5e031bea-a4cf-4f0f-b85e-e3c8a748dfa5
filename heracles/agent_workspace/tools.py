from fnmatch import fnmatch
import aiohttp
import uuid
from heracles.core.exceptions import AgentRunException
from heracles.core.config import get_env_var
from heracles.core.schema import ProjectErrorMessage
from heracles.core.utils.string import remove_ansi_escape_sequences
from .tool_base import ToolBase
from heracles.agent_workspace.paas_sdk.utils import list_middlewares_of_codezone, bind_playground_info


class Tools(ToolBase):
    """
    Encapsulates all Function Calling, tools, etc.
    工具注释请遵循numpydoc风格便于litellm提取, https://numpydoc.readthedocs.io/en/latest/format.html
    """

    async def file_tree(self, show_dirs=False) -> list[str]:
        """Returns the project file tree list

        Returns:
            list[str]: The project file tree list
        """
        file_tree_dict = await self.playground.func_call('agent_file_tree')
        file_paths = []
        max_reached = False

        def traverse(node):
            nonlocal file_paths
            nonlocal max_reached
            if node['type'] == 'FILE' and not node['path'] == '':
                file_paths.append(node['path'])
            # 当文件数量超限时，对应目录展开有提示信息节点，需要将提示信息加入到文件列表中，增强 AI 理解效果
            if node['type'] == 'DIRECTORY' and not node['path'] == '' and show_dirs:
                file_paths.append(node['path'] + '/')
            if node['type'] == 'TIPS':
                if node['name'] == 'Hidden directory not show...' and show_dirs:
                    file_paths[-1] = f'{file_paths[-1]}: hidden directory'
                elif node['name'] == 'Max 30 items per level...':
                    dir_path = file_paths[-1].split('/')[:-1]
                    file_paths.append('WARN: ' + f"{'/'.join(dir_path)}/... more files omitted in this level")
                elif node['name'] == 'Max files (1000) reached...':
                    max_reached = True
            for child in node['children']:
                traverse(child)

        traverse(file_tree_dict)

        if max_reached:
            file_paths.append('\nWARN: ...Max files (1000) reached...')
        return file_paths

    async def run_cmd(self, cmd: str) -> str:
        """Input command in Linux Shell terminal and return the command result

        Parameters
        ----------
        cmd : str
            The command to be executed, such as `ps aux` to view the process list

        Returns
        -------
        str
            Command result
        """
        # TODO: run_cmd read_file file_tree这类工具都需要做优化-限制最大长度
        # 考虑提供类似 read_file_with_lines(startline, endline) 的工具
        # 或类似 get_history_tooluse_output(tooluse_id, startline, endline) 的工具
        # make no limit timeout work
        return await self._run_cmd(cmd, 30, 24 * 60, None)

    async def run_cmd_with_soft_callback(self, cmd, soft_timeout, hard_timeout, soft_callback):
        return await self._run_cmd(cmd, soft_timeout, hard_timeout, soft_callback)

    async def _run_cmd(self, cmd, soft_timeout, hard_timeout, soft_callback, *, output_lines_limit: int = 500):
        output_lines_limit = min(max(output_lines_limit, 1), 500)
        response = await self.playground.func_call('agent_terminal_with_result', cmd, soft_timeout=soft_timeout, hard_timeout=hard_timeout, soft_callback=soft_callback)  # noqa
        if not isinstance(response, str):
            return response
        lines = response.splitlines()
        if len(lines) > output_lines_limit:
            first_30 = lines[:30]
            last_470 = lines[-470:]
            truncated_count = len(lines) - 30 - 470
            lines = first_30 + [f'... ({truncated_count} lines truncated) ...'] + last_470
        return '\n'.join(lines)

    async def create_file(self, path: str) -> bool:
        """Create a file, destination directories will be created automatically if not exist

        Parameters
        ----------
        path : str
            File path
        """
        res = await self.playground.func_call('agent_create_file', path)
        if not res['status']:
            raise AgentRunException(f"Create file failed, message: {res['message']}")
        return True

    # see more: heracles/server/clacky/ide_server_client.py
    async def write_file(self, path: str, content: str) -> bool:
        """Write to a file

        Parameters
        ----------
        path : str
            File path
        content : str
            Content to be written to the file
        """
        await self.playground.func_call('agent_write_file', path, content, open_file_after_write=True)
        return True

    async def append_file(self, path: str, text: str) -> bool:
        """Append to a file

        Parameters
        ----------
        path : str
            File path
        text : str
            Text to be appended to the file
        """
        await self.playground.func_call('agent_append_file', path, text)
        return True

    async def read_file(
        self,
        path: str,
        start_line: int = 0,
        end_line: int = 0,
        should_read_entire_file: bool = False,
        with_line_numbers: bool = True,
    ) -> str:
        """Read the contents of a text file.
        This function provides two ways to read a file:
        1. A line range (start_line(one-indexed and inclusive) and end_line(one-indexed and inclusive), you should read at least 150 lines in one call to reduce inefficiency)
        2. Set should_read_entire_file to True and start_line/end_line to 0 (Use with caution! Read file in chunks is recommended.)
        Tool Usage Notes:
        - IMPORTANT: Never read entire file unless you have asked yourself five times "Do I really need to read the entire file?" and answered "Yes" each time. Reading files in chunks is strongly recommended.
        - WARNING: This tool only supports code/text files, NEVER read binary files.
        - This call can view at most 250 lines at a time (hard-coded limit), and will read at least 150 lines (hard-coded limit) to reduce tool calls
        - `with_line_numbers` should be always set to True, as the output will be 1-indexed to help locate lines in the file.
        - When line ranges are provided, should_read_entire_file will be ignored
        - Don't worry if start_line and end_line exceeds the file length, it will be automatically adjusted
        - If the file contents you have viewed are insufficient, and you suspect they may be in lines not shown, proactively call the tool again to view those lines
        - When in doubt, call this tool again to gather more information. Remember that partial file views may miss critical dependencies, imports, or functionality

        Parameters
        ----------
        path : str
            File path, required. Can be either a relative path in the workspace or an absolute path.
        start_line : int
            The one-indexed line number to start reading from (inclusive). Defaults to 0 when using should_read_entire_file.
        end_line : int
            The one-indexed line number to end reading at (inclusive). Defaults to 0 when using should_read_entire_file. Will be automatically adjusted if exceeds file length.
        should_read_entire_file : bool
            Whether to read the entire file. For normal file reading, default is False, prefer using line ranges.
        with_line_numbers : bool
            Whether to include line numbers in the output.
            default is True

        Returns
        -------
        str
            File content with line range information. If lines are omitted, summary information will be included.
        """  # noqa
        # FIXME : 1. litellm的llm_function_to_json不支持union标注-类似 a: int | None
        if start_line or end_line:
            should_read_entire_file = False
        if should_read_entire_file:
            start_line = 0
            end_line = 0
        file_content = await self.playground.func_call('agent_read_file', path, silent=True)
        lines = file_content.splitlines()
        if with_line_numbers:
            lines = [f'{i+1:>4}: {line}' for i, line in enumerate(lines)]

        if should_read_entire_file:
            return '\n'.join(lines)

        total_lines = len(lines)

        # 计算行范围：确保至少读取150行，最多读取250行
        start_line = max(1, start_line or 1)
        min_end_line = start_line + 149  # 确保至少读取150行
        max_end_line = start_line + 249  # 确保最多读取250行
        end_line = min(total_lines, max(min_end_line, end_line or 1), max_end_line)

        selected_lines = lines[start_line - 1 : end_line]

        result = []
        if start_line > 1:
            result.append(f'... {start_line - 1} lines before ...')
        result.extend(selected_lines)
        if end_line < total_lines:
            result.append(f'... {total_lines - end_line} lines after ...')

        return '\n'.join(result)

    async def read_file_content(self, path: str, silent: bool = False, load_type: str = 'default') -> str:
        """Read file content
        This tool only support code/text files, NEVER read binary file
        no max file length limit

        Parameters
        ----------
        path : str
            File path, required

        Returns
        -------
        str
            File content
        """
        return await self.playground.func_call('agent_read_file', path, silent=silent, load_type=load_type)

    async def close_file(self) -> bool:
        """Close file in editor view (Only for Code Role to force Thinking state)"""
        return await self.playground.func_call('agent_close_file')

    async def check_path_exists(self, path: str) -> bool:
        """Check if a file or directory exists

        Parameters
        ----------
        path : str
            File or directory path, supports wildcards like '*', '*.example'

        Returns
        -------
        bool
            Whether the file or directory exists
        """
        file_list = await self.file_tree(show_dirs=True)

        for file_path in file_list:
            if fnmatch(file_path, path):
                return True
        return False

    async def move_file(self, path: str, new_path: str) -> bool:
        """Move a file, destination directories will be created automatically if not exist

        Parameters
        ----------
        path : str
            Original file path
        new_path : str
            New file path
        """
        res = await self.playground.func_call('agent_move_file', path, new_path)
        if not res['status']:
            raise AgentRunException(f"Move file failed, message: {res['message']}")
        return True

    async def delete_file(self, path: str) -> bool:
        """Delete a file

        Parameters
        ----------
        path : str
            File path
        """
        res = await self.playground.func_call('agent_delete_file', path)
        if not res['status']:
            raise AgentRunException(f"Delete file failed, message: {res['message']}")
        return True

    async def create_directory(self, path: str) -> bool:
        """Create a directory, recursively

        Parameters
        ----------
        path : str
            Directory path
        """
        res = await self.playground.func_call('agent_create_directory', path)
        if not res['status']:
            raise AgentRunException(f"Create directory failed, message: {res['message']}")
        return True

    async def move_directory(self, path: str, new_path: str) -> bool:
        """Move/rename a directory, destination directories will be created automatically if not exist

        Parameters
        ----------
        path : str
            Directory path
        new_path : str
            New directory path
        """
        res = await self.playground.func_call('agent_move_directory', path, new_path)
        if not res['status']:
            raise AgentRunException(f"Move directory failed, message: {res['message']}")
        return True

    async def delete_directory(self, path: str) -> bool:
        """Delete a directory

        Parameters
        ----------
        path : str
            Directory path
        """
        res = await self.playground.func_call('agent_delete_directory', path)
        if not res['status']:
            raise AgentRunException(f"Delete directory failed, message: {res['message']}")
        return True

    async def read_playbook(self, id: str) -> str:
        """Read a playbook

        Parameters
        ----------
        id : str
            Playbook id
        """
        playbook = self.workspace.playbook_manager.get(id)
        if not playbook:
            raise AgentRunException(f'Playbook not found, id: {id}')
        playbook.content = await playbook.render_content(self.workspace)
        return f'# {playbook.title}\n\n{playbook.content}'

    async def read_webpage(self, url: str) -> str:
        """Read a web page

        Parameters
        ----------
        url : str
            Web page url
        """
        JINA_API_KEY = get_env_var('JINA_API_KEY_OPTIONAL')
        if not JINA_API_KEY:
            raise AgentRunException('Read webpage failed, reason: JINA_API_KEY is not set')
        headers = {'Authorization': f'Bearer {JINA_API_KEY}'}
        async with aiohttp.ClientSession() as session:
            async with session.request('GET', f'https://r.jina.ai/{url}', headers=headers) as response:
                if response.status != 200:
                    raise AgentRunException(f'Failed to read web page: {url}, status code: {response.status}')
                return await response.text()

    async def search_internet(self, query: str) -> str:
        """Search the internet for information

        Parameters
        ----------
        query : str
            The query to search for

        Returns
        -------
        str
            The search results without content, you need to read the webpage via url to get the content
        """
        JINA_API_KEY = get_env_var('JINA_API_KEY_OPTIONAL')
        if not JINA_API_KEY:
            raise AgentRunException('Read webpage failed, reason: JINA_API_KEY is not set')
        headers = {'Authorization': f'Bearer {JINA_API_KEY}', 'Accept': 'application/json', 'X-Respond-With': 'no-content'}
        async with aiohttp.ClientSession() as session:
            async with session.request('GET', f'https://s.jina.ai/?q={query}', headers=headers) as response:
                if response.status != 200:
                    raise AgentRunException(f'Failed to search the internet: {query}, status code: {response.status}')
                return await response.text()

    async def validate_image_url(self, image_url: str) -> str:
        """
        Reads and validates an image URL by attempting to fetch it.
        Returns the original URL if accessible.

        Parameters
        ----------
        image_url : str
            The URL of the image.

        Returns
        -------
        str
            The original URL if accessible.
        """
        file_extensions = ('.png', '.jpg', '.jpeg', '.gif', '.webp', 'amazonaws.com') # FIXME: 需要后端添加后缀名
        if not image_url.startswith('http') or not any(ext in image_url for ext in file_extensions):
            raise AgentRunException(
                f'Invalid image URL: {image_url}, only support http or https URL, and image file extension {file_extensions}'
            )

        try:
            timeout = aiohttp.ClientTimeout(total=10)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(image_url) as response:
                    if response.status != 200:
                        raise AgentRunException(f'status code: {response.status}')
                    return image_url
        except Exception as e:
            raise AgentRunException(f'Failed to read image: {image_url}, error: {e}') from e

    async def search_related_code(self, question: str, need_expand: bool = True):
        """Related code search

        Parameters
        ----------
        question : str
            The question to search for
        need_expand : bool, optional
            Whether to expand the search query, default is True

        Returns
        -------
        list[
            FileSnippet(
            path: str = Field(description="file path")
            content: str
            row_start: int
            row_end: int),
        ]
        """
        return await self.workspace.rag_searcher.search(question, need_expand)

    async def search_codebase(self, keyword: str, regex: bool = False, whole_word_matching: bool = False, case_sensitive: bool = False):
        """Search with specific keyword or regex to locate related files containing the keyword,
        use ONLY ONE keyword or regex expression at a time; and filename search NOT supported.

        Parameters
        ----------
        keyword : str
            The keyword (or regex) to search for
        regex : bool
            Whether to use regex search
        whole_word_matching : bool
            Whether to match whole word
        case_sensitive : bool
            Whether to match case sensitive

        Returns
        -------
        list[str]
            Filepaths that match the search criteria
        """
        return await self.playground.func_call('agent_file_content_search', keyword, regex, whole_word_matching, case_sensitive)

    async def snapshot_file(self, path: str, content: str) -> str:
        """Snapshot file content to paas

        Parameters
        ----------
        path : str
            File path
        content : str
            File content

        Returns
        -------
        str
            UUID
        """
        return await self.playground.func_call('agent_snapshot_file', path, content)

    async def query_snapshot_file(self, path: str, uuid=None):
        """Read snapshot file content

        Parameters
        ----------
        path : str
            Snapshot path
        uuid : str, optional
            Snapshot uuid

        :return: list of file snapshots
        """
        snapshots = await self.playground.func_call('agent_query_snapshot_file', path, uuid)
        return snapshots

    async def query_snapshot_file_by_uuid(self, path: str, uuid: str):
        """Read snapshot file content

        Parameters
        ----------
        path : str
            Snapshot path
        uuid : str
            Snapshot uuid

        :return: file content
        """
        snapshots = await self.query_snapshot_file(path, uuid)
        if not snapshots:
            raise AgentRunException(f'File snapshot not found. path: {path}, uuid: {uuid}')
        return snapshots[0]['value']['content']

    def run_status(self):
        return self.playground.ide_server_client.run_status

    async def run_project(self):
        """Run the project, same as user clicking "RUN" button in the UI.
        Only use this tool when user explicitly request to run the project and the project is ready to run.
        """
        return await self.playground.func_call('agent_run')

    async def stop_project(self):
        """Stop the project"""
        self.workspace.playground.ide_server_client.http_ports = []
        return await self.playground.func_call('agent_stop')

    async def local_url(self) -> str | None:
        http_ports = self.playground.ide_server_client.http_ports
        if not http_ports:
            return None
        data_json = http_ports[0]
        local_url = f'http://localhost:{data_json["port"]}'
        return local_url

    async def list_changed_files(self):
        cmd = "git status --porcelain | awk '{print $2}'"
        try:
            res = await self.playground.ide_server_client.agent_terminal_with_result(cmd)
            res = res.splitlines()
        except Exception as e:
            self.logger.warning(f"Failed to get changed files, using empty list: {e}")
            res = []
        return res

    async def lint_diagnostic(self, paths: list[str]) -> dict:
        if not paths:
            return {}
        lint = await self.workspace.get_lint()
        if lint is None:
            return {}
        try:
            res = await lint.diagnostic(paths)
        except Exception as e:
            self.logger.warning(f"Failed to get lint data: {e}")
            res = {}
        return res

    async def lint_fix(self):
        lint = await self.workspace.get_lint()
        if lint is None:
            return None

        return await lint.fix()

    async def get_lint_errors(self):
        changed_files = await self.workspace.tools.list_changed_files()
        filtered_changed_files = []
        for file in changed_files:
            if file.endswith(('.ts', '.tsx', '.js', '.jsx', '.py', '.go')):
                filtered_changed_files.append(file)
        if not filtered_changed_files:
            return []

        lint_data: dict = await self.workspace.tools.lint_diagnostic(changed_files)
        if not lint_data or not lint_data.get('diagnostics'):
            self.logger.warning(f"Lint data seems empty: {lint_data}")
            return []
        for diag_dict in lint_data['diagnostics']:
            if diag_dict['severity'] == 'error':
                error_message_obj = ProjectErrorMessage(
                    title=diag_dict['message'],
                    category='lint',
                    ref_id=f"{diag_dict['source']}/{uuid.uuid4().hex[:8]}",
                    content=str(diag_dict)
                )
                self.workspace.smart_detect.errors.append(error_message_obj)
        return self.workspace.smart_detect.get_errors(category='lint')

    async def terminal_history(self, lines: int = 200) -> list[str]:
        """Fetch terminal history

        Parameters
        ----------
        lines : int, optional
            Number of history lines to fetch, default is 200

        Returns
        -------
        list[str]
            List of terminal history lines
        """
        terminal_id = self.playground.ide_server_client.agent_terminal_id
        history = await self.playground.func_call('agent_fetch_terminal_history', terminal_id, lines)
        # 去掉颜色字符, 统一换行符
        history = remove_ansi_escape_sequences(history).removeprefix('PaasNewLineSign').replace('\r\n', '\n').replace('\r', '\n')
        return history

    async def browser_goto(self, target_url: str) -> bool:
        """
        Navigate the browser to a specified URL in a new tab.

        This function opens a new browser tab and navigates to the provided URL.
        It automatically focuses on the browser component during execution.

        Parameters
        ----------
        target_url : str
            The URL to navigate to. Must include the protocol (e.g., 'https://').

        Returns
        -------
        bool
            True if the operation is successful, False otherwise.

        Examples
        --------
        >>> await browser_goto('https://www.example.com')

        Notes
        -----
        This operation will change the browser's active tab to the newly opened one.
        """
        if not await self.workspace.get_browser():
            return False
        try:
            await self.workspace.browser.goto(target_url)
        except Exception as e:
            self.logger.warning(f"Failed to goto url: {target_url}, error: {e}")
            return False
        return True

    async def browser_screenshot(self) -> str | None:
        """
        Take a screenshot of the current browser tab.

        Captures the current state of the active browser tab and returns
        the screenshot as a base64-encoded JPEG image string.
        It automatically focuses on the browser component during execution.

        Parameters
        ----------
        None

        Returns
        -------
        str | None
            Base64-encoded JPEG image string of the screenshot.
            Returns None if the screenshot operation fails.

        Examples
        --------
        >>> screenshot = await browser_screenshot()
        >>> if screenshot:
        ...     # Process the screenshot
        ...     pass
        """
        if not await self.workspace.get_browser():
            return None
        try:
            res = await self.workspace.browser.screenshot()
        except Exception as e:
            self.logger.warning(f"Failed to take browser screenshot, using None: {e}")
            res = None
        return res

    async def browser_console_logs(self) -> list[str]:
        """
        Retrieve console logs from the current browser tab.

        Fetches all console messages (logs, warnings, errors) that have been
        generated in the active browser tab. It automatically focuses on the
        browser component during execution.

        Parameters
        ----------
        None

        Returns
        -------
        list of str
            A list containing all console log messages.
            Returns an empty list if no logs are available or on failure.

        Examples
        --------
        >>> logs = await browser_console_logs()
        >>> for log in logs:
        ...     print(f"Browser console: {log}")
        """
        if not await self.workspace.get_browser():
            return []
        try:
            res = await self.workspace.browser.get_console_logs()
        except Exception as e:
            self.logger.warning(f"Failed to get browser console logs, using empty list: {e}")
            res = []
        return res

    async def list_middlewares(self) -> list[dict]:
        """List middlewares.

        Returns
        -------
        list[dict]
            The dictionary of applying the middleware
        """
        playground_id = self.workspace.playground.playground_id
        playground_info = await bind_playground_info(playground_id)
        codezone_id = playground_info['data']['codeZoneId']

        response = await list_middlewares_of_codezone(codezone_id)
        self.logger.warning(f'-> list_middlewares_of_codezone: {response}')
        middlewares = [
            {
                'name': res['name'],
                'connect_info': res['innerEnvMap'],
            }
            for res in response['data']
        ]
        return middlewares

    async def middlewares_not_empty(self) -> bool:
        """Check if middlewares are not empty

        Returns
        -------
        bool
            Whether middlewares are not empty
        """
        middlewares = await self.list_middlewares()
        return len(middlewares) > 0
